<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tarefas</title>
</head>
<body>
    <h1>Adicionar Nova Tarefa</h1>

    <form action="/criar" method="POST">
        <input type="text" name="descricao" required>
        <button type="submit">Criar Tarefa</button>
    </form>

    <h1>Lista de tarefas</h1>
    <ul>
        {% for task in tarefas %}
<!--Agora vai ser retornada uma coluna da tabela site.db-->
        <li>{{ task.descricao }}</li>
        <!--Form para atualização de registros-->
        <form action="/atualizar/{{task.id}}" method="POST" style="display: inline;">
            <input type="text" name="descricao" value="{{task.descricao}}">
            <button type="submit">Atualizar</button>
        </form>

        <!--Form para deleção de registros-->
        <form action="/deletar/{{task.id}}" method="POST" style="display: inline;">
            <button type="submit">Excluir</button>
        </form>
        {% endfor %}
    </ul>
</body>
</html>