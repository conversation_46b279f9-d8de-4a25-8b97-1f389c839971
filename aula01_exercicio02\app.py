from flask import Flask, render_template, request

app = Flask(__name__, template_folder='templates', static_folder='static')

@app.route('/calculadora', methods=['GET', 'POST'])
def homepage():
    mensagem = "Calculadora"
    resultado = None
    erro = None
    
    if request.method == 'POST':
        try:
            num1 = float(request.form['num1'])
            num2 = float(request.form['num2'])
            operacao = request.form['operacao']
            
            if operacao == 'soma':
                resultado = round (num1 + num2,2)
            elif operacao == 'subtracao':
                resultado = round (num1 - num2,2)
            elif operacao == 'multiplicacao':
                resultado = round (num1 * num2,2)
            elif operacao == 'divisao':
                if num2 == 0:
                    erro = "Erro: Divisão por zero!"
                else:
                    resultado = round (num1 / num2,2)
            
            if resultado is not None:
                resultado = round(resultado, 2)
                
        except ValueError:
            erro = "Erro: Números inválidos!"
    
    return render_template('calculadora.html', mensagem=mensagem, resultado=resultado, erro=erro)

if __name__ == '__main__':
    app.run(debug=True)
