<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Times BR</title>
    <link href="{{url_for('static', filename='style.css')}}" rel=stylesheet>
</head>
<body>
    <div class="container">
        <h1>{{ mensagem }}</h1>
    </div>
    <div class="tabela">
        <table class="tabela-estilizada">
            <thead>
                <tr>
                    <th>Posição</th>
                    <th>Time</th>
                </tr>
            </thead>
            <tbody>
                {% for time in lista %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ time }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</body>
</html>

<!--
Crie uma página web por meio do frmawork Flask, 
onde se tenha uma ista de 10 times (Implemente uma tabela). 
A página deve ser estilizada e o conteúdo centralizado. Em
um segundo momento deve-se implremntar uma estrutura de repetição FOR
para listar os times.
-->