<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tarefas</title>
</head>
<body>
    <!-- CRUD - CREATE -->
    <h1>Adicionar <PERSON></h1>
    <form action="/criar" method="POST">
        <input type="text" name="descricao" placeholder="Descrição da Tarefa" required>
        <button type="submit">Adicionar</button>
    </form>

    <!-- CRUD - READ -->
    <h1>Lista de Tarefas</h1>
    <ul>
        {% for task in tarefas %}
        <!-- Agora vai ser retornada uma coluna da tabela site.db-->
        <li>{{ task.descricao }}</li>

        <!-- Botão para atualizar a tarefa -->
        <form action="/atualizar/{{ task.id }}" method="POST" style="display: inline;">
            <!-- Puxa o valor atual da tarefa value="{{ task.descricao }}" -->
            <input type="text" name="descricao"  placeholder="Nova descrição"> 
            <button type="submit">Atualizar</button>
        </form>

        <!-- Botão para excluir a tarefa -->
        <form action="/deletar/{{ task.id }}" method="POST" style="display: inline;">
            <button type="submit">Excluir</button>
        </form>
        {% endfor %}
    </ul>
</body>
</html>