import requests
from bs4 import BeautifulSoup


res = requests.get('https://www.climatempo.com.br/')
"""
print(res.status_code)
print('\n')
print(res.headers)
print('\n')

site = BeautifulSoup(res.text, 'html.parser')
print(site.preetify())
"""

from selenium import webdriver
from selenium.webdriver.edge.service import Service 
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
import time
from selenium.webdriver.common.keys import Keys
# Adição popup
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

#service = Service(...): Cria um objeto de serviço que informa ao selenium 
#onde encontrar o arquivo executavel em seu computador
service = Service()

#edge_options = Options(): Cria um objeto para guardar as configurações do navegador
edge_options = Options()
#Maximiza a janela
edge_options.add_argument("--start-maximized") # Executa o navegador em segundo plano

navegador = webdriver.Edge(service=service, options=edge_options)
navegador.get('https://www.climatempo.com.br/')

try: 
    botao_aceitar = WebDriverWait(navegador, 10).until(EC.element_to_be_clickable((By.XPATH, '//button [text()="Aceitar"]')))
    # Clique no botão
    botao_aceitar.click()
    print("Botão 'Aceitar todos' clicado com sucesso!")
except Exception as e:
    print(f" {e}")

time.sleep(5)

campo_input = navegador.find_element(By.XPATH, '//input[conatains(@placeholder, "Buscar Destinos")]')
campo_input.send_keys('São Paulo')
campo_input.send_keys(Keys.ENTER)

input('Pressione ENTER para fechar o navegador...')
navegador.quit()