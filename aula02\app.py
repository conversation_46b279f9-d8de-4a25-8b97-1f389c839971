from flask import Flask, render_template, request, redirect
from flask_sqlalchemy import SQLAlchemy

app = Flask(__name__, template_folder='templates')

# db = SQLAlchemy(app): Aqui, cria-se o objeto que representa a conexão com o banco de dados.

app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///site.db'
db = SQLAlchemy(app)

class Tarefa(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    descricao = db.Column(db.String(100), unique=True, nullable=False)

# CRUD - READ
@app.route('/')
def index():
    # Tarefa.query.all(): Esta é a linha que interage com o BD para buscar as informações.
    tarefas = Tarefa.query.all()
    return render_template('index.html', tarefas=tarefas)

# CRUD - CREATE
@app.route('/criar', methods=['POST'])
def criar_tarefas():
    descricao = request.form['descricao']
    
    tarefa_existente = Tarefa.query.filter_by(descricao=descricao).first()
    if tarefa_existente:
        return 'Erro: Tarefa já foi cadastrada!', 400

    # Criando uma nova instância da classe Tarefa
    new_task = Tarefa(descricao = descricao)
    db.session.add(new_task)
    db.session.commit()
    return redirect('/')

# '/deletar/<int:id_tarefa>': Esta é uma rota dinamica.
# <...> Indica que esta parte  da URL é uma variável.
# Tarefas.query: Acessa o objeto de consulta do moelo Tarefas.

# CRUD - DELETE
@app.route('/deletar/<int:id_tarefa>', methods=['POST'])
def deletar_tarefas(id_tarefa):
    tarefa = Tarefa.query.get(id_tarefa)
    if tarefa:
        db.session.delete(tarefa)
        db.session.commit()
    return redirect('/')

# CRUD - UPDATE
@app.route('/atualizar/<int:id_tarefa>', methods=['POST'])
def atualizar_tarefas(id_tarefa):
    tarefa = Tarefa.query.get(id_tarefa)
    if tarefa:
        tarefa.descricao = request.form['descricao']
        db.session.commit()
    return redirect('/')

if __name__ == '__main__':
    #Cria o banco de dados
    with app.app_context():
        db.create_all()
    app.run(debug=True)