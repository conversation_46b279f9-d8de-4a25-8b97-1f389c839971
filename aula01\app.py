from flask import Flask, render_template

# O name faz uma referência ao próprio arquivo e garante que a aplicação vai rodar
app = Flask(__name__, template_folder='templates', static_folder='static')

# __name__ -> Indica o módulo atual
# template_folder -> Define a pasta onde estão os templates HTML
# static_folder -> Define a pasta onde estão os arquivos estáticos (CSS, JS, imagens)

# Rota1 - Quando se coloca somente a barra ('/'), esta é a página raiz de nosso aplicativo
@app.route('/inicio')
def homepage():
    mensagem = "Hello World!"
    # Comando HTML substituido por render_template
    return render_template('index.html', mensagem=mensagem)

if __name__ == '__main__':
    app.run(debug=True)

# O parâmetro debug=True ativa:
# - Modo debug (mostra erros detalhados)
# - Recarregamento automático quando o código é alterado.